#!/usr/bin/env python3
"""
Create sample financial data for demonstration.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_sample_data():
    """Create sample financial data."""
    print("📊 Creating sample financial data...")
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Create date range
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # Generate realistic stock price data using random walk
    initial_price = 100.0
    returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns with slight upward bias
    prices = [initial_price]
    
    for return_rate in returns[1:]:
        new_price = prices[-1] * (1 + return_rate)
        prices.append(max(new_price, 10.0))  # Minimum price of $10
    
    prices = np.array(prices)
    
    # Generate OHLC data
    opens = prices + np.random.normal(0, 0.5, len(prices))
    highs = np.maximum(prices, opens) + np.abs(np.random.normal(0, 0.3, len(prices)))
    lows = np.minimum(prices, opens) - np.abs(np.random.normal(0, 0.3, len(prices)))
    closes = prices
    
    # Generate volume data
    base_volume = 1000000
    volume_multiplier = 1 + np.random.normal(0, 0.3, len(prices))
    volumes = (base_volume * volume_multiplier).astype(int)
    volumes = np.maximum(volumes, 100000)  # Minimum volume
    
    # Create DataFrame
    sample_data = pd.DataFrame({
        'Date': dates,
        'Symbol': 'SAMPLE',
        'Open': np.round(opens, 2),
        'High': np.round(highs, 2),
        'Low': np.round(lows, 2),
        'Close': np.round(closes, 2),
        'Volume': volumes,
        'Market_Cap': np.round(closes * 1000000, 0),  # Assuming 1M shares outstanding
        'Sector': 'Technology'
    })
    
    return sample_data

def main():
    """Create and save sample data."""
    # Create sample data
    df = create_sample_data()
    
    # Save as Excel file
    excel_file = 'sample_financial_data.xlsx'
    df.to_excel(excel_file, index=False)
    
    # Save as CSV file
    csv_file = 'sample_financial_data.csv'
    df.to_csv(csv_file, index=False)
    
    print(f"✅ Sample data created successfully!")
    print(f"   Rows: {len(df):,}")
    print(f"   Columns: {len(df.columns)}")
    print(f"   Column names: {list(df.columns)}")
    print(f"   Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
    print(f"   Price range: ${df['Close'].min():.2f} to ${df['Close'].max():.2f}")
    print(f"   Files created: {excel_file}, {csv_file}")
    
    # Show sample data
    print("\n📋 Sample data preview:")
    print(df.head().to_string(index=False))
    
    return df

if __name__ == "__main__":
    main()
