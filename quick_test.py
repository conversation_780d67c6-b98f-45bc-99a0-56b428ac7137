#!/usr/bin/env python3
"""
Quick test to verify API server is working and upload endpoint is accessible.
"""

import requests
import sys

def test_health():
    """Test health endpoint."""
    try:
        print("Testing health endpoint...")
        response = requests.get('http://127.0.0.1:8000/health/', timeout=10)
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_upload_endpoint():
    """Test if upload endpoint exists."""
    try:
        print("Testing upload endpoint accessibility...")
        # Try to access upload endpoint with GET (should return method not allowed)
        response = requests.get('http://127.0.0.1:8000/upload/', timeout=10)
        print(f"Upload endpoint status: {response.status_code}")
        
        if response.status_code == 405:  # Method not allowed is expected for GET on POST endpoint
            print("✅ Upload endpoint is accessible")
            return True
        elif response.status_code == 404:
            print("❌ Upload endpoint not found (404)")
            return False
        else:
            print(f"Upload endpoint response: {response.text}")
            return True
    except Exception as e:
        print(f"❌ Upload endpoint error: {e}")
        return False

def main():
    print("🧪 Quick API Test")
    print("=" * 30)
    
    health_ok = test_health()
    upload_ok = test_upload_endpoint()
    
    if health_ok and upload_ok:
        print("\n✅ API server is working correctly!")
        return 0
    else:
        print("\n❌ API server has issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
