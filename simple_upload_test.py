#!/usr/bin/env python3
"""
Simple upload test to verify the upload functionality works.
"""

import requests
import pandas as pd
import io
import json
import time

def create_test_excel_data():
    """Create simple test data for Excel upload."""
    data = {
        'Date': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        'Stock': ['AAPL', 'AAPL', 'AAPL', 'AAPL', 'AAPL'],
        'Open': [150.0, 151.0, 149.5, 152.0, 153.5],
        'High': [152.0, 153.0, 151.0, 154.0, 155.0],
        'Low': [149.0, 150.0, 148.5, 151.0, 152.0],
        'Close': [151.0, 152.0, 150.0, 153.0, 154.0],
        'Volume': [1000000, 1100000, 950000, 1200000, 1050000]
    }
    return pd.DataFrame(data)

def test_upload():
    """Test file upload functionality."""
    print("🧪 Testing Upload Functionality")
    print("=" * 40)
    
    # Create test data
    df = create_test_excel_data()
    print(f"✅ Created test data with {len(df)} rows")
    
    # Convert to CSV for upload
    csv_buffer = io.StringIO()
    df.to_csv(csv_buffer, index=False)
    csv_content = csv_buffer.getvalue()
    
    # Prepare upload
    files = {
        'file': ('test_data.csv', csv_content, 'text/csv')
    }
    
    data = {
        'data_type': 'financial',
        'table_name': 'test_upload_data',
        'description': 'Simple test upload',
        'overwrite_existing': True,
        'validate_data': True
    }
    
    try:
        print("📤 Uploading file...")
        response = requests.post('http://127.0.0.1:8000/upload/', files=files, data=data, timeout=30)
        
        print(f"Upload response status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            upload_id = result.get('upload_id')
            print(f"✅ Upload successful! ID: {upload_id}")
            print(f"   Status: {result.get('status')}")
            print(f"   Message: {result.get('message')}")
            
            # Check status
            if upload_id:
                print("📋 Checking upload status...")
                for i in range(10):  # Check up to 10 times
                    status_response = requests.get(f'http://127.0.0.1:8000/upload/{upload_id}/status', timeout=10)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"   Status: {status_data.get('status')} ({status_data.get('progress', 0):.1f}%)")
                        
                        if status_data.get('status') in ['completed', 'failed']:
                            break
                    time.sleep(2)
                
                if status_data.get('status') == 'completed':
                    print("✅ Upload processing completed!")
                    return True
                else:
                    print(f"⚠️ Upload ended with status: {status_data.get('status')}")
                    return False
            
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_upload()
    if success:
        print("\n🎉 Upload test passed!")
    else:
        print("\n❌ Upload test failed!")
