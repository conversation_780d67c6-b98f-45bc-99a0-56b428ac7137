#!/usr/bin/env python3
"""
Demonstration of Excel upload and analysis capabilities.
"""

import sys
import json
from pathlib import Path
from excel_upload_helper import ExcelUploadHelper
from excel_analyzer import ExcelAnaly<PERSON>

def demo_upload_and_analysis():
    """Demonstrate the complete upload and analysis workflow."""
    print("🚀 Excel Upload and Analysis Demonstration")
    print("=" * 60)
    
    # Check if sample data exists
    sample_file = "sample_financial_data.xlsx"
    if not Path(sample_file).exists():
        print(f"❌ Sample file {sample_file} not found. Please run create_sample_data.py first.")
        return False
    
    print(f"📁 Using sample file: {sample_file}")
    
    # Step 1: Upload the file
    print("\n" + "="*50)
    print("STEP 1: UPLOADING EXCEL FILE")
    print("="*50)
    
    helper = ExcelUploadHelper()
    upload_result = helper.upload_excel_file(
        file_path=sample_file,
        table_name="demo_financial_data",
        description="Sample financial data for demonstration"
    )
    
    if not upload_result.get("success"):
        print(f"❌ Upload failed: {upload_result.get('error')}")
        return False
    
    print("✅ Upload successful!")
    print(f"   Method: {upload_result.get('method', 'Unknown')}")
    if 'data_shape' in upload_result:
        print(f"   Data shape: {upload_result['data_shape']}")
        print(f"   Columns: {upload_result['columns']}")
    
    # Step 2: Analyze the data
    print("\n" + "="*50)
    print("STEP 2: ANALYZING DATA")
    print("="*50)
    
    analyzer = ExcelAnalyzer()
    
    # Load data for analysis
    if 'dataframe' in upload_result:
        # Use the dataframe from direct processing
        load_result = analyzer.load_data(upload_result['dataframe'])
    else:
        # Load from file
        load_result = analyzer.load_data(sample_file)
    
    if not load_result.get("success"):
        print(f"❌ Failed to load data for analysis: {load_result.get('error')}")
        return False
    
    print("✅ Data loaded for analysis!")
    print(f"   Shape: {load_result['shape']}")
    print(f"   Memory usage: {load_result.get('memory_usage', 'Unknown')} bytes")
    
    # Perform comprehensive analysis
    print("\n📊 Performing comprehensive analysis...")
    analysis_result = analyzer.analyze_data("comprehensive")
    
    if not analysis_result.get("success"):
        print(f"❌ Analysis failed: {analysis_result.get('error')}")
        return False
    
    # Step 3: Display results
    print("\n" + "="*50)
    print("STEP 3: ANALYSIS RESULTS")
    print("="*50)
    
    # Data overview
    overview = analysis_result["data_overview"]
    print(f"\n📋 Data Overview:")
    print(f"   Rows: {overview['shape'][0]:,}")
    print(f"   Columns: {overview['shape'][1]}")
    print(f"   Memory: {overview['memory_usage_mb']} MB")
    print(f"   Column types: {len(set(overview['data_types'].values()))} different types")
    
    # Data quality
    quality = analysis_result["data_quality"]
    print(f"\n🔍 Data Quality:")
    print(f"   Overall quality: {quality['overall_quality']}")
    print(f"   Duplicate rows: {quality['duplicate_rows']}")
    print(f"   Issues found: {len(quality['issues'])}")
    if quality['issues']:
        for issue in quality['issues'][:3]:  # Show first 3 issues
            print(f"     - {issue}")
    
    # Statistical summary
    stats = analysis_result["statistical_summary"]
    if "numeric_columns" in stats:
        print(f"\n📈 Statistical Summary:")
        print(f"   Numeric columns: {len(stats['numeric_columns'])}")
        print(f"   Columns: {', '.join(stats['numeric_columns'])}")
    
    # Financial insights
    if "identified_price_columns" in analysis_result:
        price_cols = analysis_result["identified_price_columns"]
        print(f"\n💰 Financial Analysis:")
        print(f"   Price columns identified: {len(price_cols)}")
        if price_cols:
            print(f"   Price columns: {', '.join(price_cols)}")
    
    # Generate insights
    print("\n" + "="*50)
    print("STEP 4: KEY INSIGHTS")
    print("="*50)
    
    insights = analyzer.generate_insights()
    for i, insight in enumerate(insights, 1):
        print(f"{i}. {insight}")
    
    # Visualizations
    visualizations = analysis_result.get("visualizations", [])
    print(f"\n📊 Visualizations created: {len(visualizations)}")
    for viz in visualizations:
        if viz.get("type") != "error":
            print(f"   - {viz.get('title', viz.get('type', 'Unknown'))}")
    
    print("\n" + "="*60)
    print("✅ DEMONSTRATION COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    return True

def demo_direct_analysis():
    """Demonstrate direct analysis without upload."""
    print("\n🔧 Direct Analysis Demonstration (No API)")
    print("=" * 50)
    
    sample_file = "sample_financial_data.xlsx"
    if not Path(sample_file).exists():
        print(f"❌ Sample file {sample_file} not found.")
        return False
    
    analyzer = ExcelAnalyzer()
    
    # Load and analyze directly
    load_result = analyzer.load_data(sample_file)
    if load_result.get("success"):
        print(f"✅ Data loaded: {load_result['shape']}")
        
        # Quick financial analysis
        analysis = analyzer.analyze_data("financial")
        if analysis.get("success"):
            print("✅ Financial analysis completed")
            
            # Show key metrics
            if "identified_price_columns" in analysis:
                price_cols = analysis["identified_price_columns"]
                print(f"   Price columns: {price_cols}")
            
            insights = analyzer.generate_insights()
            print("   Key insights:")
            for insight in insights[:3]:
                print(f"     - {insight}")
    
    return True

def main():
    """Main demonstration function."""
    try:
        # Run main demonstration
        success = demo_upload_and_analysis()
        
        if success:
            # Also show direct analysis
            demo_direct_analysis()
            
            print("\n🎉 All demonstrations completed successfully!")
            print("\nNext steps:")
            print("1. Replace 'sample_financial_data.xlsx' with your own Excel file")
            print("2. Use: python excel_upload_helper.py your_file.xlsx")
            print("3. Or use the ExcelAnalyzer class directly in your code")
        else:
            print("\n❌ Demonstration failed. Check the error messages above.")
            
    except Exception as e:
        print(f"\n❌ Demonstration error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
